#!/usr/bin/env node

/**
 * Pre-deployment test script
 * Validates that all required files and configurations are ready for Vercel deployment
 */

import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

console.log('🚀 Pre-deployment validation for Vercel...\n');

// Check required files
const requiredFiles = [
  'vercel.json',
  'package.json',
  'dist/client/index.html',
  'api/health.js'
];

let allFilesExist = true;

console.log('📁 Checking required files:');
for (const file of requiredFiles) {
  if (existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
}

// Check vercel.json syntax
console.log('\n🔧 Validating vercel.json:');
try {
  const vercelConfig = JSON.parse(readFileSync('vercel.json', 'utf8'));
  
  // Check for invalid runtime specification
  if (vercelConfig.functions) {
    for (const [path, config] of Object.entries(vercelConfig.functions)) {
      if (config.runtime && config.runtime.includes('nodejs')) {
        console.log(`❌ Invalid runtime specification in functions.${path}: ${config.runtime}`);
        console.log('   Remove the runtime property - Vercel auto-detects Node.js');
        allFilesExist = false;
      } else {
        console.log(`✅ Function configuration for ${path} is valid`);
      }
    }
  }
  
  console.log('✅ vercel.json syntax is valid');
} catch (error) {
  console.log(`❌ vercel.json syntax error: ${error.message}`);
  allFilesExist = false;
}

// Check API files
console.log('\n🔌 Checking API endpoints:');
const apiFiles = [
  'api/health.js',
  'api/subtitles/languages/[videoId].js',
  'api/subtitles/download/[...params].js'
];

for (const file of apiFiles) {
  if (existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
}

// Final result
console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 All checks passed! Ready for Vercel deployment.');
  console.log('\nTo deploy:');
  console.log('1. npm run build');
  console.log('2. vercel --prod');
  process.exit(0);
} else {
  console.log('❌ Some checks failed. Please fix the issues above before deploying.');
  process.exit(1);
}
