import type { PricingTier } from '@/lib/stripe';

export interface Subscription {
  id: string;
  user_id: string;
  stripe_subscription_id: string;
  stripe_customer_id: string;
  status: 'active' | 'canceled' | 'incomplete' | 'incomplete_expired' | 'past_due' | 'trialing' | 'unpaid';
  tier: PricingTier;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
}

export interface UsageStats {
  user_id: string;
  videos_extracted_this_month: number;
  last_reset_date: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionWithUsage extends Subscription {
  usage: UsageStats;
}

export interface SubscriptionContextType {
  subscription: Subscription | null;
  usage: UsageStats | null;
  loading: boolean;
  error: string | null;
  canExtractVideo: (videoLengthMinutes?: number) => boolean;
  getRemainingExtractions: () => number;
  createCheckoutSession: (tier: PricingTier) => Promise<string>;
  cancelSubscription: () => Promise<void>;
  refreshSubscription: () => Promise<void>;
}
