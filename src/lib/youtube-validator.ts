export interface YouTubeUrlInfo {
  isValid: boolean;
  type: 'video' | 'invalid';
  videoId?: string;
  url: string;
  error?: string;
}

export function validateYouTubeUrl(url: string): YouTubeUrlInfo {
  if (!url || typeof url !== 'string') {
    return {
      isValid: false,
      type: 'invalid',
      url,
      error: 'Please enter a valid URL'
    };
  }

  // Clean the URL
  const cleanUrl = url.trim();

  // YouTube URL patterns
  const videoPatterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/
  ];

  const playlistPatterns = [
    /youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)/,
    /youtube\.com\/watch\?.*list=([a-zA-Z0-9_-]+)/
  ];

  // Check for playlist and reject it
  for (const pattern of playlistPatterns) {
    const match = cleanUrl.match(pattern);
    if (match) {
      return {
        isValid: false,
        type: 'invalid',
        url: cleanUrl,
        error: 'Playlist support is coming soon. Please use a single video URL for now.'
      };
    }
  }

  // Check for video
  for (const pattern of videoPatterns) {
    const match = cleanUrl.match(pattern);
    if (match) {
      const videoId = match[1];
      
      // Validate video ID format (11 characters, alphanumeric + _ -)
      if (videoId && /^[a-zA-Z0-9_-]{11}$/.test(videoId)) {
        return {
          isValid: true,
          type: 'video',
          videoId,
          url: cleanUrl
        };
      }
    }
  }

  // Check if it looks like a YouTube URL but is malformed
  if (cleanUrl.includes('youtube.com') || cleanUrl.includes('youtu.be')) {
    return {
      isValid: false,
      type: 'invalid',
      url: cleanUrl,
      error: 'Invalid YouTube URL format. Please check the URL and try again.'
    };
  }

  return {
    isValid: false,
    type: 'invalid',
    url: cleanUrl,
    error: 'Please enter a valid YouTube video URL'
  };
}

export function extractVideoId(url: string): string | null {
  const info = validateYouTubeUrl(url);
  return info.videoId || null;
}

export function isYouTubeVideo(url: string): boolean {
  const info = validateYouTubeUrl(url);
  return info.isValid && info.type === 'video';
}

// Helper function to get a clean YouTube URL
export function getCleanYouTubeUrl(videoId: string): string {
  return `https://www.youtube.com/watch?v=${videoId}`;
}

// Test cases for validation
export const testUrls = [
  // Valid video URLs
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  'https://youtu.be/dQw4w9WgXcQ',
  'https://www.youtube.com/embed/dQw4w9WgXcQ',
  'https://www.youtube.com/v/dQw4w9WgXcQ',
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=10s',

  // Invalid URLs (including playlists)
  'https://www.youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq8VGLrG',
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy6nuLMHjMZOz59Oq8VGLrG',
  'https://www.youtube.com/watch?v=invalid',
  'https://www.youtube.com/watch',
  'https://vimeo.com/123456789',
  'not a url',
  ''
];
