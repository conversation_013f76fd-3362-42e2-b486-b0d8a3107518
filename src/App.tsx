import { useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import LandingPage from './components/LandingPage';
import SubtitleExtractor from './components/SubtitleExtractor';
import Header from './components/Header';
import Footer from './components/Footer';
import TermsOfService from './components/TermsOfService';
import PrivacyPolicy from './components/PrivacyPolicy';
import Disclaimer from './components/Disclaimer';
import FAQ from './components/FAQ';
import NotFound from './components/NotFound';
import PricingPage from './components/pricing/PricingPage';
import UserDashboard from './components/dashboard/UserDashboard';

function App() {
  const [currentView, setCurrentView] = useState<'landing' | 'extractor' | 'terms' | 'privacy' | 'disclaimer' | 'faq' | 'pricing' | 'dashboard' | 'settings' | '404'>('landing');

  const handleGetStarted = () => {
    setCurrentView('extractor');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBackToHome = () => {
    setCurrentView('landing');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleNavigate = (view: 'landing' | 'extractor' | 'terms' | 'privacy' | 'disclaimer' | 'faq' | 'pricing' | 'dashboard' | 'settings' | '404') => {
    setCurrentView(view);
    // Scroll to top when navigating
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleTermsClick = () => {
    setCurrentView('terms');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handlePrivacyClick = () => {
    setCurrentView('privacy');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDisclaimerClick = () => {
    setCurrentView('disclaimer');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleFeedback = () => {
    window.open('https://docs.google.com/forms/d/e/1FAIpQLSd1zn2jbwo7UKAkoRSPIV2RIxt2ZNi4VQYLqC8S0bV7CRZe_Q/viewform?usp=dialog', '_blank');
  };

  const handle404 = () => {
    setCurrentView('404');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <AuthProvider>
      <div className="min-h-screen font-sans">
        {/* Header - Show on all pages */}
        <Header
          currentView={currentView}
          onNavigate={handleNavigate}
          onFeedback={handleFeedback}
        />

      {currentView === 'landing' && (
        <>
          <LandingPage
            onGetStarted={handleGetStarted}
            onNavigateToPricing={() => handleNavigate('pricing')}
          />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'extractor' && (
        <>
          <SubtitleExtractor onBack={handleBackToHome} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'terms' && (
        <>
          <TermsOfService onBack={handleBackToHome} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'privacy' && (
        <>
          <PrivacyPolicy onBack={handleBackToHome} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'disclaimer' && (
        <>
          <Disclaimer onBack={handleBackToHome} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'faq' && (
        <>
          <FAQ showHeader={true} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'pricing' && (
        <>
          <PricingPage onBack={handleBackToHome} />
          <Footer
            onTermsClick={handleTermsClick}
            onPrivacyClick={handlePrivacyClick}
            onDisclaimerClick={handleDisclaimerClick}
          />
        </>
      )}

      {currentView === 'dashboard' && (
        <UserDashboard onBack={handleBackToHome} onNavigate={handleNavigate} />
      )}

      {currentView === 'settings' && (
        <UserDashboard onBack={handleBackToHome} onNavigate={handleNavigate} />
      )}

      {currentView === '404' && (
        <NotFound
          onBackToHome={handleBackToHome}
          onGetStarted={handleGetStarted}
        />
      )}

        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#1e293b',
              color: '#f1f5f9',
              border: '1px solid #475569',
            },
          }}
        />
      </div>
    </AuthProvider>
  );
}

export default App;
