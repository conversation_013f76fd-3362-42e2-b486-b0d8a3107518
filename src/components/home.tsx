import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import SubtitleExtractor from "./SubtitleExtractor";
import SubtitleDisplay from "./SubtitleDisplay";
import VideoHistory from "./VideoHistory";

const Home = () => {
  const [currentVideo, setCurrentVideo] = React.useState<{
    id: string;
    title: string;
    thumbnail: string;
    subtitles: Array<{ start: number; end: number; text: string }>;
    language: string;
  } | null>(null);

  const [extractedVideos, setExtractedVideos] = React.useState<
    Array<{
      id: string;
      title: string;
      thumbnail: string;
      timestamp: Date;
      language: string;
    }>
  >([]);

  const handleSubtitlesExtracted = (data: {
    id: string;
    title: string;
    thumbnail: string;
    subtitles: Array<{ start: number; end: number; text: string }>;
    language: string;
  }) => {
    setCurrentVideo(data);

    // Add to history if not already present
    const existingIndex = extractedVideos.findIndex(
      (video) => video.id === data.id,
    );
    if (existingIndex === -1) {
      setExtractedVideos((prev) => [
        {
          id: data.id,
          title: data.title,
          thumbnail: data.thumbnail,
          timestamp: new Date(),
          language: data.language,
        },
        ...prev,
      ]);
    } else {
      // Move to top of history
      const updatedHistory = [...extractedVideos];
      const item = updatedHistory.splice(existingIndex, 1)[0];
      item.timestamp = new Date();
      item.language = data.language;
      updatedHistory.unshift(item);
      setExtractedVideos(updatedHistory);
    }
  };

  const handleHistoryItemClick = (videoId: string) => {
    const video = extractedVideos.find((v) => v.id === videoId);
    if (video && currentVideo) {
      setCurrentVideo({
        ...currentVideo,
        id: video.id,
        title: video.title,
        thumbnail: video.thumbnail,
        language: video.language,
      });
    }
  };

  return (
    <div className="min-h-screen bg-background p-4 md:p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-center mb-2">
          YouTube Subtitle Extractor
        </h1>
        <p className="text-muted-foreground text-center">
          Extract and download subtitles from any YouTube video
        </p>
      </header>

      <main className="max-w-6xl mx-auto space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Extract Subtitles</CardTitle>
          </CardHeader>
          <CardContent>
            <SubtitleExtractor
            onBack={() => {}}
              // onSubtitlesExtracted={handleSubtitlesExtracted}
            />
          </CardContent>
        </Card>

        {currentVideo &&
          currentVideo.subtitles &&
          currentVideo.subtitles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Subtitles</CardTitle>
                <div className="flex items-center gap-4">
                  <img
                    src={currentVideo.thumbnail}
                    alt={currentVideo.title}
                    className="w-24 h-auto rounded-md"
                  />
                  <div>
                    <h3 className="font-medium">{currentVideo.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      Language: {currentVideo.language}
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <SubtitleDisplay
                  subtitles={currentVideo.subtitles}
                  // videoId={currentVideo.id}
                />
              </CardContent>
            </Card>
          )}

        {extractedVideos.length > 0 && (
          <>
            <Separator />
            <div>
              <h2 className="text-xl font-semibold mb-4">Recently Extracted</h2>
              <VideoHistory
                // videos={extractedVideos}
                // onVideoClick={handleHistoryItemClick}
              />
            </div>
          </>
        )}
      </main>

      <footer className="mt-12 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} YouTube Subtitle Extractor</p>
      </footer>
    </div>
  );
};

export default Home;
