
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SubtitleItem {
  start: number;
  end: number;
  text: string;
}

interface SubtitleDisplayProps {
  subtitles?: SubtitleItem[];
  videoTitle?: string;
  onDownload?: (format: string) => void;
}

const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")},${ms.toString().padStart(3, "0")}`;
};

const SubtitleDisplay = ({
  subtitles = [],
  videoTitle = "Video Subtitles",
  onDownload = () => {},
}: SubtitleDisplayProps) => {
  return (
    <div className="w-full bg-background">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">{videoTitle}</h2>
          <div className="flex items-center gap-2">
            <Select defaultValue="srt" onValueChange={onDownload}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="srt">Download SRT</SelectItem>
                <SelectItem value="txt">Download TXT</SelectItem>
                <SelectItem value="vtt">Download VTT</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => onDownload("srt")}>Download</Button>
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <ScrollArea className="h-[400px] p-4">
              {subtitles.length > 0 ? (
                <div className="space-y-4">
                  {subtitles.map((subtitle, index) => (
                    <div
                      key={index}
                      className="border-b border-border pb-2 last:border-0"
                    >
                      <div className="text-xs text-muted-foreground">
                        {formatTime(subtitle.start)} →{" "}
                        {formatTime(subtitle.end)}
                      </div>
                      <div className="mt-1 text-sm">{subtitle.text}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex h-full items-center justify-center text-muted-foreground">
                  No subtitles available. Please extract subtitles first.
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SubtitleDisplay;
