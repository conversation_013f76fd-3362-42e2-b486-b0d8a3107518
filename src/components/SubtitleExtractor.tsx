import { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Play,
  Download,
  Eye,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowLeft,
  EyeOff,
  Search,
  Crown,
  Lock
} from 'lucide-react';
import { validateYouTubeUrl, YouTubeUrlInfo } from '@/lib/youtube-validator';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import LoginButton from './auth/LoginButton';
import toast from 'react-hot-toast';

interface Language {
  code: string;
  name: string;
  isAutoGenerated: boolean;
}

interface SubtitleExtractorProps {
  onBack: () => void;
}

const SubtitleExtractor = ({ onBack }: SubtitleExtractorProps) => {
  const { user } = useAuth();
  const { subscription, canExtractVideo, getRemainingExtractions } = useSubscription();

  const [url, setUrl] = useState('');
  const [urlInfo, setUrlInfo] = useState<YouTubeUrlInfo | null>(null);
  const [languages, setLanguages] = useState<Language[]>([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [isValidating, setIsValidating] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [step, setStep] = useState<'input' | 'languages' | 'extracting' | 'preview'>('input');
  const [subtitles, setSubtitles] = useState<any[]>([]);
  const [videoInfo, setVideoInfo] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(true);
  const [previewFormat, setPreviewFormat] = useState<'vtt' | 'txt'>('vtt');
  const [languageSearch, setLanguageSearch] = useState('');

  // Filter languages based on search
  const filteredLanguages = useMemo(() => {
    if (!languageSearch.trim()) {
      return languages;
    }
    return languages.filter(lang =>
      lang.name.toLowerCase().includes(languageSearch.toLowerCase()) ||
      lang.code.toLowerCase().includes(languageSearch.toLowerCase())
    );
  }, [languages, languageSearch]);

  const handleUrlChange = (value: string) => {
    setUrl(value);
    if (value.trim()) {
      setIsValidating(true);
      // Debounce validation
      setTimeout(() => {
        const info = validateYouTubeUrl(value);
        setUrlInfo(info);
        setIsValidating(false);

        if (!info.isValid && value.trim()) {
          toast.error(info.error || 'Invalid YouTube URL');
        }
      }, 500);
    } else {
      setUrlInfo(null);
      setIsValidating(false);
    }
  };



  // Function to get available languages
  const getAvailableLanguages = async () => {
    if (!urlInfo?.isValid) return;

    setIsExtracting(true);
    setExtractionProgress(0);

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setExtractionProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch(`/api/subtitles/languages/${urlInfo.videoId}`);
      const data = await response.json();

      clearInterval(progressInterval);
      setExtractionProgress(100);

      if (response.ok) {
        setLanguages(data.languages || []);
        setVideoInfo(data);
        setStep('languages');
        toast.success(`Found ${data.languages?.length || 0} available languages!`);
      } else {
        toast.error(data.error || 'Failed to get available languages');
        setStep('input');
      }
    } catch (error) {
      toast.error('Network error. Please try again');
      setStep('input');
    } finally {
      setIsExtracting(false);
      setExtractionProgress(0);
    }
  };

  const extractSubtitles = async () => {
    if (!urlInfo?.isValid || !selectedLanguage) return;

    // Check authentication
    if (!user) {
      toast.error('Please sign in to extract subtitles');
      return;
    }

    // Check subscription and usage limits
    if (!canExtractVideo()) {
      const remaining = getRemainingExtractions();
      if (remaining === 0) {
        toast.error('You have reached your monthly extraction limit. Please upgrade your plan.');
      } else {
        toast.error('Please subscribe to a plan to extract subtitles');
      }
      return;
    }

    setIsExtracting(true);
    setExtractionProgress(0);
    setStep('extracting');

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setExtractionProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch(`/api/subtitles/download/${urlInfo.videoId}-${selectedLanguage}`);
      const data = await response.json();

      clearInterval(progressInterval);
      setExtractionProgress(100);

      if (response.ok) {
        setSubtitles(data.subtitles || []);
        setVideoInfo(data);
        setStep('preview');
        toast.success('Subtitles extracted successfully!');
      } else {
        toast.error(data.error || 'Failed to extract subtitles');
        setStep('languages');
      }
    } catch (error) {
      toast.error('Network error. Please try again.');
      console.error(error)
      setStep('languages');
    } finally {
      setIsExtracting(false);
      setExtractionProgress(0);
    }
  };

  const downloadSubtitles = (format: 'vtt' | 'txt') => {
    if (!subtitles.length) return;

    let content = '';
    const filename = `${videoInfo?.title?.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'subtitles'}.${format}`;

    switch (format) {
      case 'vtt':
        content = 'WEBVTT\n\n';
        subtitles.forEach((sub, index) => {
          // Convert time format to HH:MM:SS.mmm
          const formatTime = (timeStr: any) => {
            const str = String(timeStr ?? '');
          
            if (str.includes(':') && str.split(':').length === 3) {
              return str;
            }
          
            const seconds = parseFloat(str);
            
            if (isNaN(seconds)) {
              return '00:00:00.000'; // fallback
            }            
            
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = (seconds % 60).toFixed(3);
          
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.padStart(6, '0')}`;
          };
          
          console.log(sub);
          const startTime = formatTime(sub.start);
          const endTime = formatTime(sub.end);
          content += `${index + 1}\n${startTime} --> ${endTime}\n${sub.text}\n\n`;
        });
        break;
      case 'txt':
        // Add metadata header
        content = `Title: ${videoInfo?.title || 'Unknown'}\n`;
        content += `Video ID: ${urlInfo?.videoId || 'Unknown'}\n`;
        content += `Channel: ${videoInfo?.uploader || videoInfo?.channel || 'Unknown'}\n\n`;
        content += '---\n\n';

        // Add timestamped content
        subtitles.forEach(sub => {
          // Convert timestamps to [MM:SS - MM:SS] format
          const formatTimestamp = (timeStr: string) => {
            const seconds = parseFloat(timeStr);
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
          };

          const startTime = formatTimestamp(sub.start);
          const endTime = formatTimestamp(sub.end);
          content += `[${startTime} - ${endTime}] ${sub.text}\n\n`;
        });
        break;
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success(`Downloaded ${filename}`);
  };

  const generatePreviewContent = (format: 'vtt' | 'txt') => {
    if (!subtitles.length) return '';

    switch (format) {
      case 'vtt':
        let vttContent = 'WEBVTT\n\n';
        subtitles.forEach((sub, index) => {
          // Convert time format to HH:MM:SS.mmm
          const formatTime = (timeStr: any) => {
            const str = String(timeStr ?? '');
          
            if (str.includes(':') && str.split(':').length === 3) {
              return str;
            }
          
            const seconds = parseFloat(str);
            
            if (isNaN(seconds)) {
              return '00:00:00.000'; // fallback
            }            
            
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = (seconds % 60).toFixed(3);
          
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${secs.padStart(6, '0')}`;
          };

          const startTime = formatTime(sub.start);
          const endTime = formatTime(sub.end);
          vttContent += `${index + 1}\n${startTime} --> ${endTime}\n${sub.text}\n\n`;
        });
        return vttContent;
      case 'txt':
        // Add metadata header
        let txtContent = `Title: ${videoInfo?.title || 'Unknown'}\n`;
        txtContent += `Video ID: ${urlInfo?.videoId || 'Unknown'}\n`;
        txtContent += `Channel: ${videoInfo?.uploader || videoInfo?.channel || 'Unknown'}\n\n`;
        txtContent += '---\n\n';

        // Add timestamped content
        subtitles.forEach(sub => {
          // Convert timestamps to [MM:SS - MM:SS] format
          const formatTimestamp = (timeStr: string) => {
            const seconds = parseFloat(timeStr);
            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
          };

          const startTime = formatTimestamp(sub.start);
          const endTime = formatTimestamp(sub.end);
          txtContent += `[${startTime} - ${endTime}] ${sub.text}\n\n`;
        });
        return txtContent;
      default:
        return '';
    }
  };

  const resetExtractor = () => {
    setUrl('');
    setUrlInfo(null);
    setLanguages([]);
    setSelectedLanguage('en');
    setStep('input');
    setSubtitles([]);
    setVideoInfo(null);
    setShowPreview(false);
    setPreviewFormat('vtt');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-4 sm:py-8">
      <div className="max-w-4xl mx-auto px-3 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-6 sm:mb-8"
        >
          <Button
            variant="ghost"
            onClick={onBack}
            className="text-white hover:text-purple-400 text-sm sm:text-base p-2 sm:p-3"
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
            <span className="hidden xs:inline">Back to Home</span>
            <span className="xs:hidden">Back</span>
          </Button>

          <div className="flex items-center space-x-1 sm:space-x-2">
            <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'input' ? 'bg-purple-500' : (step === 'languages' || step === 'extracting' || step === 'preview') ? 'bg-green-500' : 'bg-gray-600'}`} />
            <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'languages' ? 'bg-purple-500' : (step === 'extracting' || step === 'preview') ? 'bg-green-500' : 'bg-gray-600'}`} />
            <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'extracting' ? 'bg-purple-500' : step === 'preview' ? 'bg-green-500' : 'bg-gray-600'}`} />
            <div className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full ${step === 'preview' ? 'bg-purple-500' : 'bg-gray-600'}`} />
          </div>
        </motion.div>

        {/* Subscription Status */}
        {user && subscription && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <Card className="bg-slate-800/60 backdrop-blur-sm border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Crown className="w-4 h-4 text-yellow-500" />
                    <span className="text-white font-medium capitalize">{subscription.tier} Plan</span>
                  </div>
                  <div className="text-sm text-gray-300">
                    {getRemainingExtractions() === -1
                      ? 'Unlimited extractions'
                      : `${getRemainingExtractions()} extractions remaining`
                    }
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Authentication Prompt */}
        {!user && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6"
          >
            <Card className="bg-slate-800/60 backdrop-blur-sm border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Lock className="w-4 h-4 text-orange-500" />
                    <span className="text-white font-medium">Sign in required</span>
                  </div>
                  <LoginButton size="sm" variant="outline" />
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        <AnimatePresence mode="wait">
          {/* Step 1: URL Input */}
          {step === 'input' && (
            <motion.div
              key="input"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader className="pb-4 sm:pb-6">
                  <CardTitle className="text-white text-xl sm:text-2xl">
                    Enter YouTube URL
                  </CardTitle>
                  <p className="text-gray-300 text-sm sm:text-base">
                    Paste a YouTube video URL to get started
                  </p>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  <div className="space-y-2">
                    <Input
                      placeholder="https://www.youtube.com/watch?v=..."
                      value={url}
                      onChange={(e) => handleUrlChange(e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 text-sm sm:text-base"
                    />

                    {isValidating && (
                      <div className="flex items-center text-gray-400 text-xs sm:text-sm">
                        <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin" />
                        Validating URL...
                      </div>
                    )}

                    {urlInfo && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center space-x-2 flex-wrap"
                      >
                        {urlInfo.isValid ? (
                          <>
                            <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
                            <span className="text-green-400 text-sm sm:text-base">Valid YouTube Video</span>
                            <Badge variant="secondary" className="bg-purple-600 text-white text-xs">
                              <Play className="w-3 h-3 mr-1" /> Video
                            </Badge>
                          </>
                        ) : (
                          <>
                            <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-400" />
                            <span className="text-red-400 text-sm sm:text-base">{urlInfo.error}</span>
                          </>
                        )}
                      </motion.div>
                    )}
                  </div>

                  <Button
                    onClick={getAvailableLanguages}
                    disabled={!urlInfo?.isValid || isExtracting}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base"
                  >
                    {isExtracting ? (
                      <>
                        <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-2 animate-spin" />
                        <span className="hidden xs:inline">Getting Languages...</span>
                        <span className="xs:hidden">Loading...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                        Get Available Languages
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Language Selection */}
          {step === 'languages' && (
            <motion.div
              key="languages"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader className="pb-4 sm:pb-6">
                  <CardTitle className="text-white text-xl sm:text-2xl">
                    Select Language
                  </CardTitle>
                  <p className="text-gray-300 text-sm sm:text-base">
                    Choose the subtitle language you want to extract
                  </p>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  {videoInfo && (
                    <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg">
                      <img
                        src={videoInfo.thumbnail}
                        alt={videoInfo.title}
                        className="w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-semibold text-sm sm:text-base truncate">{videoInfo.title}</h3>
                        <p className="text-gray-400 text-xs sm:text-sm">
                          Video • {languages.length} languages available
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <label className="text-white font-medium text-sm sm:text-base">Available Languages:</label>

                    {/* Search Input */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3 sm:w-4 sm:h-4" />
                      <Input
                        placeholder="Search languages..."
                        value={languageSearch}
                        onChange={(e) => setLanguageSearch(e.target.value)}
                        className="bg-slate-700 border-slate-600 text-white placeholder-gray-400 pl-9 sm:pl-10 text-sm sm:text-base"
                      />
                    </div>

                    {/* Language List */}
                    <div className="max-h-48 sm:max-h-64 overflow-y-auto bg-slate-700 rounded-lg border border-slate-600">
                      {filteredLanguages.length === 0 ? (
                        <div className="p-3 sm:p-4 text-center text-gray-400 text-sm sm:text-base">
                          No languages found
                        </div>
                      ) : (
                        filteredLanguages.map((lang) => (
                          <div
                            key={lang.code}
                            onClick={() => setSelectedLanguage(lang.code)}
                            className={`p-3 cursor-pointer hover:bg-slate-600 transition-colors ${
                              selectedLanguage === lang.code ? 'bg-purple-600' : ''
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white text-sm sm:text-base">{lang.name}</span>
                              <div className="flex items-center space-x-1 sm:space-x-2">
                                {lang.isAutoGenerated && (
                                  <Badge variant="secondary" className="bg-orange-600 text-white text-xs">
                                    Auto
                                  </Badge>
                                )}
                                {selectedLanguage === lang.code && (
                                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                                )}
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setStep('input')}
                      className="flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base"
                    >
                      <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                      Back
                    </Button>
                    <Button
                      onClick={extractSubtitles}
                      disabled={!selectedLanguage}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base"
                    >
                      <Download className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                      Extract Subtitles
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Extraction Progress */}
          {step === 'extracting' && (
            <motion.div
              key="extracting"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader className="pb-4 sm:pb-6">
                  <CardTitle className="text-white text-xl sm:text-2xl">
                    Extracting Subtitles
                  </CardTitle>
                  <p className="text-gray-300 text-sm sm:text-base">
                    Please wait while we extract and process the subtitles
                  </p>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  <div className="text-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="inline-block"
                    >
                      <Loader2 className="w-12 h-12 sm:w-16 sm:h-16 text-purple-500" />
                    </motion.div>
                    <p className="text-white mt-3 sm:mt-4 text-base sm:text-lg">Processing...</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-xs sm:text-sm">
                      <span className="text-gray-300">Progress</span>
                      <span className="text-purple-400">{extractionProgress}%</span>
                    </div>
                    <Progress value={extractionProgress} className="h-2" />
                  </div>

                  <div className="text-center text-gray-400 text-xs sm:text-sm px-2">
                    {extractionProgress < 30 && "Fetching video information..."}
                    {extractionProgress >= 30 && extractionProgress < 60 && "Downloading subtitle data..."}
                    {extractionProgress >= 60 && extractionProgress < 90 && "Processing and cleaning subtitles..."}
                    {extractionProgress >= 90 && "Almost done..."}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Preview and Download */}
          {step === 'preview' && (
            <motion.div
              key="preview"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
                <CardHeader className="pb-4 sm:pb-6">
                  <CardTitle className="text-white text-xl sm:text-2xl">
                    Subtitles Ready!
                  </CardTitle>
                  <p className="text-gray-300 text-sm sm:text-base">
                    Preview and download your extracted subtitles
                  </p>
                </CardHeader>
                <CardContent className="space-y-4 sm:space-y-6">
                  {videoInfo && (
                    <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-slate-700/50 rounded-lg">
                      <img
                        src={videoInfo.thumbnail}
                        alt={videoInfo.title}
                        className="w-16 h-12 sm:w-20 sm:h-15 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-semibold text-sm sm:text-base truncate">{videoInfo.title}</h3>
                        <p className="text-gray-400 text-xs sm:text-sm">
                          {subtitles.length} subtitle entries • {languages.find(l => l.code === selectedLanguage)?.name || 'English'}
                        </p>
                      </div>
                      <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-green-400 flex-shrink-0" />
                    </div>
                  )}

                  {/* Format Selection and Download */}
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                      <label className="text-white font-medium text-sm sm:text-base">Select Format:</label>
                      <div className="flex space-x-2">
                        {(['vtt', 'txt'] as const).map((format) => (
                          <Button
                            key={format}
                            variant={previewFormat === format ? "default" : "outline"}
                            size="sm"
                            onClick={() => setPreviewFormat(format)}
                            className={`text-xs sm:text-sm px-3 sm:px-4 ${previewFormat === format
                              ? "bg-purple-600 hover:bg-purple-700"
                              : "border-slate-600 text-black hover:bg-slate-700 hover:text-white"
                            }`}
                          >
                            {format.toUpperCase()}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={() => downloadSubtitles(previewFormat)}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-3 sm:py-4 text-sm sm:text-base"
                    >
                      <Download className="w-3 h-3 sm:w-4 sm:h-4 mr-2" />
                      Download {previewFormat.toUpperCase()} Format
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                      <label className="text-white font-medium text-sm sm:text-base">Preview ({previewFormat.toUpperCase()}):</label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowPreview(!showPreview)}
                        className="border-slate-600 text-black hover:bg-slate-700 hover:text-white text-xs sm:text-sm self-start sm:self-auto"
                      >
                        {showPreview ? (
                          <><EyeOff className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" /> Hide</>
                        ) : (
                          <><Eye className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" /> Show</>
                        )}
                      </Button>
                    </div>

                    {showPreview && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="max-h-64 sm:max-h-96 overflow-y-auto bg-slate-900 rounded-lg p-3 sm:p-4 border border-slate-600"
                      >
                        <pre className="text-xs sm:text-sm text-white whitespace-pre-wrap font-mono">
                          {generatePreviewContent(previewFormat)}
                        </pre>
                      </motion.div>
                    )}
                  </div>

                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                    <Button
                      variant="outline"
                      onClick={resetExtractor}
                      className="flex-1 border-slate-600 text-black hover:bg-slate-700 hover:text-white py-2 sm:py-3 text-sm sm:text-base"
                    >
                      Extract Another
                    </Button>
                    <Button
                      onClick={() => setStep('input')}
                      className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 py-2 sm:py-3 text-sm sm:text-base"
                    >
                      Start Over
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default SubtitleExtractor;