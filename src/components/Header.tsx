import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Download, MessageSquare, Home, HelpCircle, CreditCard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import LoginButton from './auth/LoginButton';
import UserMenu from './auth/UserMenu';

interface HeaderProps {
  currentView: string;
  onNavigate: (view: 'landing' | 'extractor' | 'terms' | 'privacy' | 'disclaimer' | 'faq' | 'pricing' | 'dashboard' | 'settings') => void;
  onFeedback: () => void;
}

const Header = ({ currentView, onNavigate, onFeedback }: HeaderProps) => {
  const { user } = useAuth();
  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-slate-900/95 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-50"
    >
      <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14 sm:h-16">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center space-x-2 sm:space-x-3 cursor-pointer"
            onClick={() => onNavigate('landing')}
          >
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
              <Download className="w-4 h-4 sm:w-6 sm:h-6 text-white" />
            </div>
            <div className="hidden xs:block">
              <h1 className="text-lg sm:text-xl font-bold text-white">DownloadYTSubtitles</h1>
              <p className="text-xs text-gray-400 hidden sm:block">YouTube Subtitle Extractor</p>
            </div>
            <div className="block xs:hidden">
              <h1 className="text-sm font-bold text-white">DYTS</h1>
            </div>
          </motion.div>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Button
              variant={currentView === 'landing' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('landing')}
              className={currentView === 'landing' 
                ? 'bg-purple-600 hover:bg-purple-700' 
                : 'text-gray-300 hover:text-white'
              }
            >
              <Home className="w-4 h-4 mr-2" />
              Home
            </Button>
            
            <Button
              variant={currentView === 'extractor' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('extractor')}
              className={currentView === 'extractor' 
                ? 'bg-purple-600 hover:bg-purple-700' 
                : 'text-gray-300 hover:text-white'
              }
            >
              <Download className="w-4 h-4 mr-2" />
              Extract
            </Button>

            <Button
              variant={currentView === 'faq' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('faq')}
              className={currentView === 'faq'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }
            >
              <HelpCircle className="w-4 h-4 mr-2" />
              FAQ
            </Button>

            <Button
              variant={currentView === 'pricing' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('pricing')}
              className={currentView === 'pricing'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }
            >
              <CreditCard className="w-4 h-4 mr-2" />
              Pricing
            </Button>
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-2 sm:space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={onFeedback}
              className="border-purple-500 text-purple-400 hover:bg-purple-500 hover:text-white text-xs sm:text-sm px-2 sm:px-3"
            >
              <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2" />
              <span className="hidden xs:inline">Feedback</span>
              <span className="xs:hidden">FB</span>
            </Button>

            {user ? (
              <UserMenu onNavigate={onNavigate} />
            ) : (
              <LoginButton
                variant="outline"
                size="sm"
                className="border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white"
              />
            )}

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onNavigate('extractor')}
                className="text-gray-300 hover:text-white p-2"
              >
                <Download className="w-4 h-4 sm:w-5 sm:h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-3">
          <div className="flex items-center justify-center space-x-2 overflow-x-auto">
            <Button
              variant={currentView === 'landing' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('landing')}
              className={`text-xs px-3 py-2 ${currentView === 'landing'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }`}
            >
              Home
            </Button>

            <Button
              variant={currentView === 'extractor' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('extractor')}
              className={`text-xs px-3 py-2 ${currentView === 'extractor'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }`}
            >
              Extract
            </Button>

            <Button
              variant={currentView === 'faq' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('faq')}
              className={`text-xs px-3 py-2 ${currentView === 'faq'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }`}
            >
              FAQ
            </Button>

            <Button
              variant={currentView === 'pricing' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onNavigate('pricing')}
              className={`text-xs px-3 py-2 ${currentView === 'pricing'
                ? 'bg-purple-600 hover:bg-purple-700'
                : 'text-gray-300 hover:text-white'
              }`}
            >
              Pricing
            </Button>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
