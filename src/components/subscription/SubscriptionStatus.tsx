import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useSubscription } from '@/hooks/useSubscription';
import { PRICING_TIERS } from '@/lib/stripe';
import { Crown, Calendar, TrendingUp, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';

const SubscriptionStatus: React.FC = () => {
  const { subscription, usage, loading } = useSubscription();

  if (loading) {
    return (
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-slate-700 rounded w-1/3"></div>
            <div className="h-8 bg-slate-700 rounded w-1/2"></div>
            <div className="h-4 bg-slate-700 rounded w-2/3"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-500" />
            No Active Subscription
          </CardTitle>
          <CardDescription className="text-gray-300">
            Subscribe to a plan to start extracting YouTube subtitles
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const tier = PRICING_TIERS[subscription.tier as keyof typeof PRICING_TIERS];
  const currentUsage = usage?.videos_extracted_this_month || 0;
  const usageLimit = tier?.limits.videosPerMonth || 0;
  const usagePercentage = usageLimit === -1 ? 0 : (currentUsage / usageLimit) * 100;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-600';
      case 'trialing':
        return 'bg-blue-600';
      case 'past_due':
        return 'bg-yellow-600';
      case 'canceled':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'trialing':
        return 'Trial';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-4">
      {/* Subscription Overview */}
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Crown className="w-5 h-5 text-yellow-500" />
            {tier?.name} Plan
          </CardTitle>
          <CardDescription className="text-gray-300 flex items-center gap-2">
            <Badge className={getStatusColor(subscription.status)}>
              {getStatusText(subscription.status)}
            </Badge>
            <span>${tier?.price}/month</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-2 text-sm text-gray-400 mb-1">
                <Calendar className="w-4 h-4" />
                Current Period
              </div>
              <div className="text-white">
                {format(new Date(subscription.current_period_start), 'MMM d')} - {format(new Date(subscription.current_period_end), 'MMM d, yyyy')}
              </div>
            </div>
            <div>
              <div className="flex items-center gap-2 text-sm text-gray-400 mb-1">
                <TrendingUp className="w-4 h-4" />
                Next Billing
              </div>
              <div className="text-white">
                {subscription.cancel_at_period_end 
                  ? 'Cancels at period end' 
                  : format(new Date(subscription.current_period_end), 'MMM d, yyyy')
                }
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Statistics */}
      <Card className="bg-slate-800/80 backdrop-blur-sm border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Usage This Month</CardTitle>
          <CardDescription className="text-gray-300">
            Track your subtitle extraction usage
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-400">Videos Extracted</span>
              <span className="text-white font-medium">
                {currentUsage} {usageLimit === -1 ? '' : `/ ${usageLimit}`}
              </span>
            </div>
            {usageLimit !== -1 && (
              <Progress 
                value={usagePercentage} 
                className="h-2"
              />
            )}
            {usageLimit === -1 && (
              <div className="text-sm text-green-400 font-medium">
                ✨ Unlimited extractions
              </div>
            )}
          </div>

          {tier && (
            <div className="pt-4 border-t border-slate-700">
              <h4 className="text-sm font-medium text-white mb-2">Plan Features</h4>
              <ul className="space-y-1 text-sm text-gray-400">
                {tier.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SubscriptionStatus;
