import express, { Request, Response } from 'express';
import cors from 'cors';
import youtubedl from 'youtube-dl-exec';
import path from 'path';
import {dirname} from 'path';
import { fileURLToPath } from 'url';


const app = express();
const PORT = process.env.PORT || 3001;
const __dirname = dirname(fileURLToPath(import.meta.url));
const clientPath = path.join(__dirname, '../client');

// Middleware
app.use(cors());
app.use(express.json());

// Types



interface LanguageOption {
  code: string;
  name: string;
  isAutoGenerated?: boolean;
}

interface Subtitle {
  start: number;
  end: number;
  text: string;
}









// API Routes



// Helper function to get available languages for a video
const getAvailableLanguages = async (videoId: string): Promise<LanguageOption[]> => {
  const availableLanguages: LanguageOption[] = [];

  console.log(`🔍 Checking available subtitles for video: ${videoId}`);

  try {
    // Get video info including available subtitles
    const videoInfo = await youtubedl(`https://www.youtube.com/watch?v=${videoId}`, {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      preferFreeFormats: true,
      addHeader: ['referer:youtube.com', 'user-agent:googlebot']
    }) as any;

    if (videoInfo.subtitles) {
      // Add manual subtitles
      Object.keys(videoInfo.subtitles).forEach(langCode => {
        const langName = getLanguageName(langCode);
        availableLanguages.push({
          code: langCode,
          name: langName,
          isAutoGenerated: false
        });
      });
    }

    if (videoInfo.automatic_captions) {
      // Add auto-generated subtitles
      Object.keys(videoInfo.automatic_captions).forEach(langCode => {
        // Don't duplicate if we already have manual subtitles for this language
        if (!availableLanguages.find(lang => lang.code === langCode)) {
          const langName = getLanguageName(langCode);
          availableLanguages.push({
            code: langCode,
            name: langName,
            isAutoGenerated: true
          });
        }
      });
    }

    console.log(`✅ Found ${availableLanguages.length} subtitle languages`);
  } catch (error) {
    console.log(`❌ Error checking subtitles: ${error}`);
  }

  return availableLanguages;
};

// Helper function to get language name from code
const getLanguageName = (code: string): string => {
  const languageMap: { [key: string]: string } = {
    'en': 'English',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi',
    'tr': 'Turkish',
    'pl': 'Polish',
    'nl': 'Dutch',
    'sv': 'Swedish',
    'da': 'Danish',
    'no': 'Norwegian',
    'fi': 'Finnish',
    'cs': 'Czech',
    'hu': 'Hungarian',
    'ro': 'Romanian',
    'bg': 'Bulgarian',
    'hr': 'Croatian',
    'sk': 'Slovak',
    'sl': 'Slovenian',
    'et': 'Estonian',
    'lv': 'Latvian',
    'lt': 'Lithuanian',
    'uk': 'Ukrainian',
    'el': 'Greek',
    'he': 'Hebrew',
    'th': 'Thai',
    'vi': 'Vietnamese',
    'id': 'Indonesian',
    'ms': 'Malay',
    'tl': 'Filipino',
    'sw': 'Swahili',
    'af': 'Afrikaans',
    'sq': 'Albanian',
    'am': 'Amharic',
    'hy': 'Armenian',
    'az': 'Azerbaijani',
    'eu': 'Basque',
    'be': 'Belarusian',
    'bn': 'Bengali',
    'bs': 'Bosnian',
    'ca': 'Catalan',
    'ceb': 'Cebuano',
    'ny': 'Chichewa',
    'co': 'Corsican',
    'eo': 'Esperanto',
    'fa': 'Persian',
    'fy': 'Frisian',
    'gl': 'Galician',
    'ka': 'Georgian',
    'gu': 'Gujarati',
    'ht': 'Haitian Creole',
    'ha': 'Hausa',
    'haw': 'Hawaiian',
    'hmn': 'Hmong',
    'is': 'Icelandic',
    'ig': 'Igbo',
    'ga': 'Irish',
    'jw': 'Javanese',
    'kn': 'Kannada',
    'kk': 'Kazakh',
    'km': 'Khmer',
    'rw': 'Kinyarwanda',
    'ku': 'Kurdish',
    'ky': 'Kyrgyz',
    'lo': 'Lao',
    'la': 'Latin',
    'lb': 'Luxembourgish',
    'mk': 'Macedonian',
    'mg': 'Malagasy',
    'ml': 'Malayalam',
    'mt': 'Maltese',
    'mi': 'Maori',
    'mr': 'Marathi',
    'mn': 'Mongolian',
    'my': 'Myanmar (Burmese)',
    'ne': 'Nepali',
    'ps': 'Pashto',
    'pa': 'Punjabi',
    'sm': 'Samoan',
    'gd': 'Scots Gaelic',
    'sr': 'Serbian',
    'st': 'Sesotho',
    'sn': 'Shona',
    'sd': 'Sindhi',
    'si': 'Sinhala',
    'so': 'Somali',
    'su': 'Sundanese',
    'tg': 'Tajik',
    'ta': 'Tamil',
    'tt': 'Tatar',
    'te': 'Telugu',
    'uz': 'Uzbek',
    'cy': 'Welsh',
    'xh': 'Xhosa',
    'yi': 'Yiddish',
    'yo': 'Yoruba',
    'zu': 'Zulu'
  };

  return languageMap[code] || code.toUpperCase();
};

// Helper function to download and parse subtitle content
const downloadAndParseSubtitles = async (subtitleUrl: string): Promise<Subtitle[]> => {
  try {
    const response = await fetch(subtitleUrl);
    const content = await response.text();

    // Parse VTT format
    if (content.includes('WEBVTT')) {
      return parseVTTContent(content);
    }

    // Try to parse as JSON3 format (YouTube's format)
    try {
      const jsonData = JSON.parse(content);
      if (jsonData.events) {
        return parseJSON3Content(jsonData);
      }
    } catch (e) {
      // Not JSON, continue with VTT parsing
    }

    // Fallback to VTT parsing
    return parseVTTContent(content);
  } catch (error) {
    console.error('Error downloading/parsing subtitles:', error);
    return [];
  }
};

// Parse VTT subtitle format
const parseVTTContent = (content: string): Subtitle[] => {
  const subtitles: Subtitle[] = [];
  const lines = content.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Look for timestamp lines (format: 00:00:00.000 --> 00:00:03.000)
    if (line.includes('-->')) {
      const [startTime, endTime] = line.split('-->').map(t => t.trim());
      const start = parseVTTTime(startTime);
      const end = parseVTTTime(endTime);

      // Get the text lines that follow
      const textLines: string[] = [];
      i++; // Move to next line after timestamp

      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) {
          // Clean up any VTT formatting tags
          const cleanText = textLine.replace(/<[^>]*>/g, '').trim();
          if (cleanText) {
            textLines.push(cleanText);
          }
        }
        i++;
      }
      i--; // Step back one since the loop will increment

      if (textLines.length > 0) {
        subtitles.push({
          start,
          end,
          text: textLines.join(' ')
        });
      }
    }
  }

  return subtitles;
};

// Parse JSON3 subtitle format (YouTube's format)
const parseJSON3Content = (jsonData: any): Subtitle[] => {
  const subtitles: Subtitle[] = [];

  if (jsonData.events) {
    for (const event of jsonData.events) {
      if (event.segs) {
        let text = '';
        for (const seg of event.segs) {
          if (seg.utf8) {
            text += seg.utf8;
          }
        }

        if (text.trim()) {
          subtitles.push({
            start: event.tStartMs / 1000,
            end: (event.tStartMs + event.dDurationMs) / 1000,
            text: text.trim()
          });
        }
      }
    }
  }

  return subtitles;
};

// Parse VTT time format to seconds
const parseVTTTime = (timeStr: string): number => {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parseFloat(parts[2]);
    return hours * 3600 + minutes * 60 + seconds;
  }
  return 0;
};

// Get available subtitle languages for a video
app.get('/api/subtitles/languages/:videoId', async (req: Request, res: Response) => {
  try {
    const { videoId } = req.params;

    if (!videoId) {
      return res.status(400).json({ error: 'Video ID is required' });
    }

    console.log(`Fetching subtitle languages for video: ${videoId}`);

    // Get available languages using the new caption extractor
    const availableLanguages = await getAvailableLanguages(videoId);

    if (availableLanguages.length === 0) {
      return res.status(404).json({ error: 'No subtitles available for this video' });
    }

    // Get video title and thumbnail
    let videoTitle = 'YouTube Video';
    let thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault for better compatibility

    try {
      // Get the title from the YouTube page directly
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
      const html = await response.text();
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        videoTitle = titleMatch[1].replace(' - YouTube', '').trim();
      }
    } catch (titleError) {
      console.log('Could not fetch video title, using default');
    }

    res.json({
      videoId,
      title: videoTitle,
      thumbnail: thumbnailUrl,
      languages: availableLanguages
    });

  } catch (error) {
    console.error('Error fetching subtitle languages:', error);
    res.status(500).json({
      error: 'Failed to fetch subtitle languages',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Download subtitles for a specific language
app.get('/api/subtitles/download/:videoId/:langCode', async (req: Request, res: Response) => {
  try {
    const { videoId, langCode } = req.params;

    if (!videoId || !langCode) {
      return res.status(400).json({ error: 'Video ID and language code are required' });
    }

    console.log(`Downloading subtitles for video: ${videoId}, language: ${langCode}`);

    // Get video info and extract subtitles using youtube-dl-exec
    const videoInfo = await youtubedl(`https://www.youtube.com/watch?v=${videoId}`, {
      dumpSingleJson: true,
      noCheckCertificates: true,
      noWarnings: true,
      addHeader: ['referer:youtube.com', 'user-agent:googlebot']
    }) as any;

    let subtitles: Subtitle[] = [];

    // Try to get manual subtitles first, then auto-generated
    if (videoInfo.subtitles && videoInfo.subtitles[langCode]) {
      console.log(`Found manual subtitles for ${langCode}`);
      const subtitleUrl = videoInfo.subtitles[langCode][0]?.url;
      if (subtitleUrl) {
        subtitles = await downloadAndParseSubtitles(subtitleUrl);
      }
    } else if (videoInfo.automatic_captions && videoInfo.automatic_captions[langCode]) {
      console.log(`Found auto-generated subtitles for ${langCode}`);
      const subtitleUrl = videoInfo.automatic_captions[langCode][0]?.url;
      if (subtitleUrl) {
        subtitles = await downloadAndParseSubtitles(subtitleUrl);
      }
    } else {
      return res.status(404).json({ error: 'Subtitles not found for the specified language' });
    }

    // Get video title and thumbnail
    let videoTitle = 'YouTube Video';
    let thumbnailUrl = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`; // Use hqdefault for better compatibility

    try {
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
      const html = await response.text();
      const titleMatch = html.match(/<title>([^<]+)<\/title>/);
      if (titleMatch && titleMatch[1]) {
        videoTitle = titleMatch[1].replace(' - YouTube', '').trim();
      }
    } catch (titleError) {
      console.log('Could not fetch video title, using default');
    }

    res.json({
      videoId,
      title: videoTitle,
      thumbnail: thumbnailUrl,
      language: 'English',
      subtitles
    });

  } catch (error) {
    console.error('Error downloading subtitles:', error);
    res.status(500).json({
      error: 'Failed to download subtitles',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint
app.get('/api/health', (_req: Request, res: Response) => {
  res.json({ status: 'OK', message: 'YouTube Subtitle Extractor API is running' });
});

// API 404 handler - for any unmatched API routes
app.all('/api/*splat', (req: Request, res: Response) => {
  res.status(404).json({ 
    error: 'API endpoint not found',
    path: req.path
  });
});

// ✅ Serve static files (built by Vite) in production
if (process.env.NODE_ENV === 'production') {
  console.log(`Serving static files from ${clientPath}`);
  app.use(express.static(clientPath));
  
  // For any other routes, serve the index.html to let React Router handle it
  app.get('/', (_req, res) => {
    res.sendFile(path.join(clientPath, 'index.html'));
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/api/health`);
});
