{"version": 2, "buildCommand": "npm run build:client", "outputDirectory": "dist/client", "installCommand": "npm ci", "functions": {"api/**/*.js": {"maxDuration": 30, "memory": 1024}}, "rewrites": [{"source": "/((?!api).*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}