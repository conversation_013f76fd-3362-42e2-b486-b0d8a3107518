#!/usr/bin/env node

/**
 * Health check script for YouTube Subtitle Extractor
 * Usage: node health-check.js [url]
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const url = process.argv[2] || 'http://localhost:3001';

async function healthCheck() {
  try {
    console.log(`🔍 Checking health of: ${url}`);
    
    const response = await fetch(`${url}/api/health`);
    const data = await response.json();
    
    if (response.ok && data.status === 'OK') {
      console.log('✅ Server is healthy!');
      console.log(`📊 Status: ${data.status}`);
      console.log(`💬 Message: ${data.message}`);
      process.exit(0);
    } else {
      console.log('❌ Server health check failed');
      console.log(`📊 Status: ${response.status}`);
      console.log(`💬 Response:`, data);
      process.exit(1);
    }
  } catch (error) {
    console.log('❌ Health check failed with error:');
    console.log(error.message);
    process.exit(1);
  }
}

healthCheck();
