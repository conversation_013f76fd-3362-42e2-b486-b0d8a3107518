# Files to exclude from Vercel deployment (but keep for build)
# Only exclude files that are not needed for build or runtime

# Documentation
*.md
*.log

# Exclude server directory - Vercel uses serverless functions in /api instead
server/

# Keep src/, vite.config.ts, tsconfig.json - needed for build!

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Development dependencies (keep only production dependencies)
# Note: Vercel will install from package.json anyway

# Keep these for deployment:
# dist/
# package.json
# package-lock.json
# vercel.json
# server-prod.js
