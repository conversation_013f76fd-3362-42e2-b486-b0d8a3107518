# YouTube Subtitle Extractor - Deployment Guide

## Vercel Deployment (Recommended)

### Prerequisites
- Node.js 18+ installed locally
- Vercel account
- Git repository

### Quick Deploy to Vercel

1. **<PERSON>lone and prepare the repository:**
   ```bash
   git clone <your-repo-url>
   cd YTSubtitleExtractor
   npm install
   npm run build
   ```

2. **Deploy to Vercel:**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel --prod
   ```

3. **Or deploy via Vercel Dashboard:**
   - Connect your GitHub repository to Vercel
   - Vercel will automatically detect the configuration from `vercel.json`
   - Build and deployment will happen automatically

### Manual Server Deployment

If you prefer to deploy on your own server:

1. **Build the application:**
   ```bash
   npm install
   npm run build
   ```

2. **Start the production server:**
   ```bash
   npm start
   # or
   node dist/server/index.js
   # or
   npm run serve
   ```

3. **Environment Variables:**
   ```bash
   export NODE_ENV=production
   export PORT=3000
   ```

### Configuration Files

- **`vercel.json`**: Vercel serverless deployment configuration
- **`api/`**: Serverless function endpoints for Vercel
- **`dist/client/`**: Static React app files
- **`server-prod.js`**: Alternative production server entry point
- **`.vercelignore`**: Files to exclude from Vercel deployment
- **`Dockerfile`**: Container deployment configuration
- **`health-check.js`**: Health monitoring script
- **`validate-vercel.js`**: Configuration validation script

### Available Scripts

- `npm run build` - Build both client and server
- `npm run start` - Start production server
- `npm run serve` - Alternative production start
- `npm run vercel-build` - Vercel-specific build command
- `npm run health` - Health check script
- `npm run validate:vercel` - Validate vercel.json configuration
- `npm run docker:build` & `npm run docker:run` - Docker commands

### Build Process

The build process creates:
- `dist/client/`: Static frontend files (React app)
- `dist/server/`: Node.js server bundle

### API Endpoints (Serverless Functions)

- `GET /api/health` - Health check
- `GET /api/subtitles/languages/[videoId]` - Get available languages
- `GET /api/subtitles/download/[videoId]/[langCode]` - Download subtitles

### Serverless Architecture

The application now uses Vercel's serverless functions:
- **Static files** served from `dist/client/`
- **API endpoints** as individual serverless functions in `api/`
- **Automatic scaling** and **zero cold starts** for better performance

### Dependencies

Key production dependencies:
- `youtube-dl-exec`: YouTube subtitle extraction
- `express`: Web server
- `cors`: Cross-origin resource sharing

### Troubleshooting

1. **Build fails**: Ensure all TypeScript files compile correctly
2. **Server doesn't start**: Check Node.js version (requires 18+)
3. **YouTube extraction fails**: Verify youtube-dl-exec is properly installed

### Performance Notes

- Server function timeout: 30 seconds
- Memory allocation: 1024MB
- Caching: Static assets cached for 1 year
- Runtime: Node.js 20.x

### Security

- CORS enabled for API endpoints
- No sensitive data in client bundle
- Production environment variables set
