#!/usr/bin/env node

/**
 * Production server for YouTube Subtitle Extractor
 * This file serves as the entry point for production deployments
 */

import { createRequire } from 'module';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Set production environment
process.env.NODE_ENV = 'production';

// Import and start the server
try {
  // Import the built server
  const serverPath = join(__dirname, 'dist', 'server', 'index.js');
  await import(serverPath);
  
  console.log('✅ YouTube Subtitle Extractor server started successfully');
} catch (error) {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
}
