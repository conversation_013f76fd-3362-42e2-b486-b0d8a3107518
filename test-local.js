#!/usr/bin/env node

/**
 * Local Development Test Script
 * Tests both client and server functionality
 */

import http from 'http';
import https from 'https';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function testEndpoint(url, description) {
  return new Promise((resolve) => {
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          log(colors.green, `✅ ${description} - OK`);
          resolve(true);
        } else {
          log(colors.red, `❌ ${description} - Status: ${res.statusCode}`);
          resolve(false);
        }
      });
    }).on('error', (err) => {
      log(colors.red, `❌ ${description} - Error: ${err.message}`);
      resolve(false);
    });
  });
}

async function runTests() {
  log(colors.blue, '🧪 Running Local Development Tests...\n');
  
  const tests = [
    {
      url: 'http://localhost:5173',
      description: 'Client (Vite Dev Server)'
    },
    {
      url: 'http://localhost:3001/api/health',
      description: 'Server Health Check'
    }
  ];
  
  let passed = 0;
  
  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    if (result) passed++;
  }
  
  log(colors.blue, `\n📊 Test Results: ${passed}/${tests.length} passed`);
  
  if (passed === tests.length) {
    log(colors.green, '🎉 All tests passed! Your local environment is working correctly.');
    log(colors.yellow, '\n📝 Next steps:');
    log(colors.yellow, '   1. Open http://localhost:5173 in your browser');
    log(colors.yellow, '   2. Check browser console for any errors');
    log(colors.yellow, '   3. Test the YouTube subtitle extraction functionality');
  } else {
    log(colors.red, '⚠️  Some tests failed. Check the output above for details.');
  }
}

runTests().catch(console.error);
